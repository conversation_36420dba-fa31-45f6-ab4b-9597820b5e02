package com.cryptoapp.infrastructure.config;

import io.r2dbc.postgresql.PostgresqlConnectionConfiguration;
import io.r2dbc.postgresql.PostgresqlConnectionFactory;
import io.r2dbc.spi.ConnectionFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.data.r2dbc.config.AbstractR2dbcConfiguration;
import org.springframework.data.r2dbc.convert.R2dbcCustomConversions;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.connection.R2dbcTransactionManager;
import org.springframework.transaction.ReactiveTransactionManager;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.r2dbc.postgresql.codec.Json;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Database configuration for R2DBC with PostgreSQL.
 * 
 * This configuration provides reactive database connectivity with optimized
 * connection pooling and JSON conversion support for complex data types.
 */
@Slf4j
@Configuration
@EnableR2dbcRepositories(basePackages = "com.cryptoapp.infrastructure.repository")
public class DatabaseConfig extends AbstractR2dbcConfiguration {

    @Value("${spring.r2dbc.url}")
    private String databaseUrl;

    @Value("${spring.r2dbc.username:}")
    private String username;

    @Value("${spring.r2dbc.password:}")
    private String password;

    @Override
    @Bean
    public ConnectionFactory connectionFactory() {
        // Parse the R2DBC URL to extract connection details
        String cleanUrl = databaseUrl.replace("r2dbc:postgresql://", "");

        // Handle URLs with embedded credentials
        String hostAndPort;
        String actualUsername = username;
        String actualPassword = password;

        if (cleanUrl.contains("@")) {
            // Extract credentials from URL
            String[] credentialsParts = cleanUrl.split("@");
            String credentials = credentialsParts[0];
            cleanUrl = credentialsParts[1]; // Remove credentials from URL

            if (credentials.contains(":")) {
                String[] userPass = credentials.split(":", 2); // Split only on first colon
                actualUsername = userPass[0];
                String passwordPart = userPass[1];

                // Handle Neon endpoint workaround in password field
                if (passwordPart.startsWith("endpoint=") && passwordPart.contains(";")) {
                    // Extract actual password after the semicolon
                    String[] endpointAndPassword = passwordPart.split(";", 2);
                    actualPassword = endpointAndPassword[1];
                    log.info("Using Neon endpoint workaround for SNI support");
                } else {
                    actualPassword = passwordPart;
                }
            }
        }

        String[] parts = cleanUrl.split("/");
        hostAndPort = parts[0];
        String databaseAndParams = parts[1];
        
        // Extract database name and parameters
        String database;
        String sslMode = null;
        if (databaseAndParams.contains("?")) {
            String[] dbParts = databaseAndParams.split("\\?");
            database = dbParts[0];
            String params = dbParts[1];
            
            // Parse SSL mode parameter
            if (params.contains("sslmode=")) {
                String[] paramPairs = params.split("&");
                for (String param : paramPairs) {
                    if (param.startsWith("sslmode=")) {
                        sslMode = param.split("=")[1];
                        break;
                    }
                }
            }
        } else {
            database = databaseAndParams;
        }
        
        String[] hostPortParts = hostAndPort.split(":");
        String host = hostPortParts[0];
        int port = Integer.parseInt(hostPortParts[1]);

        log.info("Configuring R2DBC connection to {}:{}/{} with SSL mode: {}", host, port, database, sslMode);
        log.info("Original R2DBC URL: {}", databaseUrl);

        PostgresqlConnectionConfiguration.Builder configBuilder = PostgresqlConnectionConfiguration.builder()
                .host(host)
                .port(port)
                .database(database)
                .username(actualUsername)
                .password(actualPassword);
        
        // Configure SSL based on the sslmode parameter
        if ("require".equals(sslMode) || "prefer".equals(sslMode)) {
            configBuilder.enableSsl();
            log.info("SSL enabled for database connection");
        } else if ("disable".equals(sslMode)) {
            log.info("SSL disabled for database connection");
        } else {
            // Default behavior - enable SSL for remote connections
            configBuilder.enableSsl();
            log.info("SSL enabled by default for database connection");
        }

        return new PostgresqlConnectionFactory(configBuilder.build());
    }

    @Bean
    public ReactiveTransactionManager transactionManager(ConnectionFactory connectionFactory) {
        return new R2dbcTransactionManager(connectionFactory);
    }

    @Bean
    @Override
    public R2dbcCustomConversions r2dbcCustomConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new JsonToMapConverter());
        converters.add(new MapToJsonConverter());
        converters.add(new JsonToListConverter());
        converters.add(new ListToJsonConverter());
        converters.add(new JsonToListOfMapsConverter());
        converters.add(new ListOfMapsToJsonConverter());
        return new R2dbcCustomConversions(getStoreConversions(), converters);
    }

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    /**
     * Converter for reading JSON columns as Map<String, Object>
     */
    @ReadingConverter
    public static class JsonToMapConverter implements Converter<Json, Map<String, Object>> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public Map<String, Object> convert(Json json) {
            try {
                return objectMapper.readValue(json.asString(), new TypeReference<Map<String, Object>>() {});
            } catch (JsonProcessingException e) {
                log.error("Error converting JSON to Map", e);
                return Map.of();
            }
        }
    }

    /**
     * Converter for writing Map<String, Object> as JSON columns
     */
    @WritingConverter
    public static class MapToJsonConverter implements Converter<Map<String, Object>, Json> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public Json convert(Map<String, Object> source) {
            try {
                return Json.of(objectMapper.writeValueAsString(source));
            } catch (JsonProcessingException e) {
                log.error("Error converting Map to JSON", e);
                return Json.of("{}");
            }
        }
    }

    /**
     * Converter for reading JSON columns as List<String>
     */
    @ReadingConverter
    public static class JsonToListConverter implements Converter<Json, List<String>> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public List<String> convert(Json json) {
            try {
                return objectMapper.readValue(json.asString(), new TypeReference<List<String>>() {});
            } catch (JsonProcessingException e) {
                log.error("Error converting JSON to List", e);
                return List.of();
            }
        }
    }

    /**
     * Converter for writing List<String> as JSON columns
     */
    @WritingConverter
    public static class ListToJsonConverter implements Converter<List<String>, Json> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public Json convert(List<String> source) {
            try {
                return Json.of(objectMapper.writeValueAsString(source));
            } catch (JsonProcessingException e) {
                log.error("Error converting List to JSON", e);
                return Json.of("[]");
            }
        }
    }

    /**
     * Converter for reading JSON columns as List<Map<String, String>>
     */
    @ReadingConverter
    public static class JsonToListOfMapsConverter implements Converter<Json, List<Map<String, String>>> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public List<Map<String, String>> convert(Json json) {
            try {
                return objectMapper.readValue(json.asString(), new TypeReference<List<Map<String, String>>>() {});
            } catch (JsonProcessingException e) {
                log.error("Error converting JSON to List of Maps", e);
                return List.of();
            }
        }
    }

    /**
     * Converter for writing List<Map<String, String>> as JSON columns
     */
    @WritingConverter
    public static class ListOfMapsToJsonConverter implements Converter<List<Map<String, String>>, Json> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public Json convert(List<Map<String, String>> source) {
            try {
                return Json.of(objectMapper.writeValueAsString(source));
            } catch (JsonProcessingException e) {
                log.error("Error converting List of Maps to JSON", e);
                return Json.of("[]");
            }
        }
    }
}