# Development Profile Configuration
spring:
  config:
    activate:
      on-profile: dev
  
  # R2DBC Configuration for PostgreSQL (Development)
  r2dbc:
    url: r2dbc:postgresql://neondb_owner:endpoint=ep-orange-pond-a27eado1-pooler;<EMAIL>:5432/neondb?sslmode=require
    pool:
      initial-size: 5
      max-size: 20
      max-idle-time: 10m
      max-acquire-time: 5s
      max-create-connection-time: 10s
      validation-query: "SELECT 1"
      background-eviction-interval: 30s

  # Redis Configuration (Development)
  data:
    redis:
      url: redis://default:<EMAIL>:6379
      ssl:
        enabled: true
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 4
          min-idle: 2
          max-wait: 2000ms

  # Flyway Configuration (Development) - Temporarily disabled due to SNI issues
  flyway:
    enabled: false
    url: *******************************************************************************************************
    user: neondb_owner
    password: endpoint=ep-orange-pond-a27eado1-pooler;npg_okyEf7ZOJ1Ui
    locations: classpath:db/migration
    baseline-on-migrate: true
    mixed: true
    enabled: true

# Application-specific configuration for development
app:
  # RSS Configuration (More frequent for development)
  rss:
    sync-interval: 300000        # 5 minutes for development
    batch-size: 15               # Smaller batches for development
    
  # LLM Configuration (Enable LM Studio for development)
  llm:
    provider: "lmstudio"         # Use local LM Studio for development
    fallback-enabled: true
    timeout: 60s
    providers:
      lmstudio:
        base-url: "http://127.0.0.1:1234"  # Local LM Studio
        model: "default"
        enabled: true
      gemini:
        api-key: ${GEMINI_API_KEY:}
        model: "gemini-1.5-flash"
        enabled: true
      openai:
        api-key: ${OPENAI_API_KEY:}
        model: "gpt-3.5-turbo"
        enabled: false
      deepseek:
        api-key: ${DEEPSEEK_API_KEY:}
        model: "deepseek-chat"
        enabled: false
    
  # Performance settings (Reduced for development)
  performance:
    max-concurrent-rss: 3
    max-concurrent-llm: 2
    circuit-breaker-threshold: 3

  # Scheduling (Enable for development)
  scheduling:
    enabled: true

# Logging Configuration (More verbose for development)
logging:
  level:
    com.cryptoapp: DEBUG
    com.cryptoapp.infrastructure.scheduler: INFO
    com.cryptoapp.infrastructure.service: INFO
    reactor.netty: DEBUG
    org.springframework.data.r2dbc: DEBUG
    org.springframework.scheduling: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Management endpoints (More exposed for development)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,scheduledtasks,cache
  endpoint:
    health:
      show-details: always
    scheduledtasks:
      enabled: true