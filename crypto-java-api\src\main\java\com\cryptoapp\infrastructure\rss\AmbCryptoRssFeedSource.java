package com.cryptoapp.infrastructure.rss;

import com.cryptoapp.domain.Article;
import com.rometools.rome.feed.synd.SyndEntry;
import com.rometools.rome.feed.synd.SyndFeed;
import com.rometools.rome.io.SyndFeedInput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.StringReader;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * RSS feed source for AMBCrypto.
 * 
 * AMBCrypto is a cryptocurrency news platform that provides market analysis,
 * price predictions, and comprehensive coverage of the digital asset ecosystem.
 */
@Slf4j
@Component
public class AMBCryptoRssFeedSource implements RssFeedSource {

    private final WebClient webClient;
    
    public AMBCryptoRssFeedSource(@Qualifier("rssWebClient") WebClient webClient) {
        this.webClient = webClient;
    }
    
    private static final String FEED_URL = "https://ambcrypto.com/feed/";
    private static final String SOURCE_NAME = "AMBCrypto";
    private static final String BASE_URL = "https://ambcrypto.com";

    @Override
    public String getSourceName() {
        return SOURCE_NAME;
    }

    @Override
    public String getFeedUrl() {
        return FEED_URL;
    }

    @Override
    public Flux<Article> fetchArticles() {
        log.info("Fetching articles from AMBCrypto RSS feed: {}", FEED_URL);
        
        return webClient.get()
            .uri(FEED_URL)
            .retrieve()
            .bodyToMono(String.class)
            .flatMapMany(this::parseRssFeed)
            .doOnNext(article -> log.debug("Parsed AMBCrypto article: {}", article.getTitle()))
            .doOnError(error -> log.error("Error fetching AMBCrypto RSS feed", error))
            .onErrorResume(error -> {
                log.warn("Failed to fetch AMBCrypto RSS feed, returning empty flux", error);
                return Flux.empty();
            });
    }

    @Override
    public Mono<Article> fetchArticle(String url) {
        log.debug("Fetching individual AMBCrypto article: {}", url);
        
        return webClient.get()
            .uri(url)
            .retrieve()
            .bodyToMono(String.class)
            .map(this::parseArticleContent)
            .doOnError(error -> log.error("Error fetching AMBCrypto article: {}", url, error))
            .onErrorReturn(createErrorArticle(url));
    }

    @Override
    public boolean isHealthy() {
        try {
            return webClient.get()
                .uri(FEED_URL)
                .retrieve()
                .toBodilessEntity()
                .map(response -> response.getStatusCode().is2xxSuccessful())
                .block();
        } catch (Exception e) {
            log.warn("AMBCrypto RSS feed health check failed", e);
            return false;
        }
    }

    @Override
    public int getPriority() {
        return 5; // Lower priority for AMBCrypto
    }

    private Flux<Article> parseRssFeed(String feedContent) {
        try {
            SyndFeedInput input = new SyndFeedInput();
            SyndFeed feed = input.build(new StringReader(feedContent));
            
            List<SyndEntry> entries = feed.getEntries();
            log.info("Parsed {} entries from AMBCrypto RSS feed", entries.size());
            
            return Flux.fromIterable(entries)
                .map(this::convertToArticle)
                .filter(article -> article != null);
                
        } catch (Exception e) {
            log.error("Error parsing AMBCrypto RSS feed", e);
            return Flux.error(e);
        }
    }

    private Article convertToArticle(SyndEntry entry) {
        try {
            Article.ArticleBuilder builder = Article.builder()
                .title(cleanText(entry.getTitle()))
                .sourceUrl(entry.getLink())
                .source(SOURCE_NAME)
                .guid(entry.getUri())
                .category("Analysis"); // AMBCrypto focuses on analysis and predictions

            // Handle publication date
            Date publishedDate = entry.getPublishedDate();
            if (publishedDate != null) {
                builder.publishedDate(LocalDateTime.ofInstant(
                    publishedDate.toInstant(), 
                    ZoneId.systemDefault()
                ));
            } else {
                builder.publishedDate(LocalDateTime.now());
            }

            // Handle description/summary
            if (entry.getDescription() != null) {
                String summary = cleanHtml(entry.getDescription().getValue());
                builder.summary(summary);
            }

            // Handle author
            if (entry.getAuthor() != null) {
                builder.author(cleanText(entry.getAuthor()));
            }

            // Handle categories/tags
            if (entry.getCategories() != null && !entry.getCategories().isEmpty()) {
                String category = entry.getCategories().get(0).getName();
                builder.category(cleanText(category));
            }

            // Extract image URL from content or enclosures
            String imageUrl = extractImageUrl(entry);
            if (imageUrl != null) {
                builder.imageUrl(imageUrl);
            }

            // Set default values
            builder.createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now());

            // Build body content
            Map<String, Object> body = Map.of(
                "content", entry.getDescription() != null ? 
                    cleanHtml(entry.getDescription().getValue()) : "",
                "raw_content", entry.getDescription() != null ? 
                    entry.getDescription().getValue() : ""
            );
            builder.body(body);

            Article article = builder.build();
            article.generateSlugFromTitle();
            
            return article;
            
        } catch (Exception e) {
            log.error("Error converting AMBCrypto RSS entry to article: {}", entry.getTitle(), e);
            return null;
        }
    }

    private Article parseArticleContent(String htmlContent) {
        // This would be used for fetching full article content
        // For now, return a basic article structure
        return Article.builder()
            .title("Full Article Content")
            .source(SOURCE_NAME)
            .summary("Full article content from AMBCrypto")
            .publishedDate(LocalDateTime.now())
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
    }

    private String extractImageUrl(SyndEntry entry) {
        // Try to extract image from enclosures
        if (entry.getEnclosures() != null && !entry.getEnclosures().isEmpty()) {
            return entry.getEnclosures().get(0).getUrl();
        }

        // Try to extract image from content
        if (entry.getDescription() != null) {
            String content = entry.getDescription().getValue();
            try {
                org.jsoup.nodes.Document doc = Jsoup.parse(content);
                org.jsoup.nodes.Element img = doc.selectFirst("img");
                if (img != null) {
                    String src = img.attr("src");
                    // Make sure it's an absolute URL
                    if (src.startsWith("//")) {
                        return "https:" + src;
                    } else if (src.startsWith("/")) {
                        return BASE_URL + src;
                    }
                    return src;
                }
            } catch (Exception e) {
                log.debug("Could not extract image from AMBCrypto entry content", e);
            }
        }

        return null;
    }

    private String cleanText(String text) {
        if (text == null) return null;
        return text.trim().replaceAll("\\s+", " ");
    }

    private String cleanHtml(String html) {
        if (html == null) return null;
        
        // Clean HTML and preserve basic formatting
        String cleaned = Jsoup.clean(html, Safelist.basicWithImages());
        
        // Convert to plain text for summary
        return Jsoup.parse(cleaned).text().trim();
    }

    private Article createErrorArticle(String url) {
        return Article.builder()
            .title("Error fetching article")
            .source(SOURCE_NAME)
            .sourceUrl(url)
            .summary("Failed to fetch article content")
            .publishedDate(LocalDateTime.now())
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
    }
}