spring:
  application:
    name: crypto-webflux-api
  
  profiles:
    active: dev
  
  # R2DBC Configuration for PostgreSQL
  r2dbc:
    url: r2dbc:postgresql://neondb_owner:<EMAIL>:5432/neondb?sslmode=require

    pool:
      initial-size: 5
      max-size: 20
      max-idle-time: 10m
      max-acquire-time: 5s
      max-create-connection-time: 10s
      validation-query: "SELECT 1"
      background-eviction-interval: 30s

  # Redis Configuration
  data:
    redis:
      url: redis://default:<EMAIL>:6379
      ssl:
        enabled: true
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms

  # WebFlux Configuration
  webflux:
    multipart:
      max-in-memory-size: 1MB
      max-disk-usage-per-part: 10MB

  # Jackson Configuration
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
      write-bigdecimal-as-plain: true
    deserialization:
      fail-on-unknown-properties: false
    modules:
      - com.fasterxml.jackson.datatype.jsr310.JavaTimeModule

  # Flyway Configuration (for migrations)
  flyway:
    url: *******************************************************************************************************
    user: neondb_owner
    password: npg_okyEf7ZOJ1Ui

    locations: classpath:db/migration
    baseline-on-migrate: true
    mixed: true

# Server Configuration with Netty optimizations
server:
  port: 8080
  netty:
    connection-timeout: 5s
    h2c-max-content-length: 10MB
    max-keep-alive-requests: 1000
  compression:
    enabled: true
    mime-types: application/json,text/plain,text/html,application/javascript,text/css
    min-response-size: 1024

# Reactor Netty Configuration
reactor:
  netty:
    pool:
      max-connections: 500
      max-idle-time: 30s
      max-life-time: 60s
      pending-acquire-timeout: 5s

# Application-specific configuration
app:
  rss:
    sync-interval: 3600000        # 1 hour in milliseconds
    batch-size: 20               # Articles per sync batch
    sources:
      - name: "CoinDesk"
        url: "https://feeds.feedburner.com/CoinDesk"
        enabled: true
      - name: "CoinTelegraph"
        url: "https://cointelegraph.com/rss"
        enabled: true
      - name: "CryptoSlate"
        url: "https://cryptoslate.com/feed/"
        enabled: true
      - name: "The Block"
        url: "https://www.theblockcrypto.com/rss.xml"
        enabled: true
      - name: "Bitcoin Magazine"
        url: "https://bitcoinmagazine.com/feed"
        enabled: true
      - name: "BeInCrypto"
        url: "https://beincrypto.com/feed/"
        enabled: true
      - name: "AMBCrypto"
        url: "https://ambcrypto.com/feed/"
        enabled: true
  
  llm:
    provider: "gemini"           # primary provider: gemini, openai, deepseek, lmstudio
    fallback-enabled: true
    timeout: 30s
    providers:
      gemini:
        api-key: ${GEMINI_API_KEY:}
        model: "gemini-1.5-flash"
        base-url: "https://generativelanguage.googleapis.com"
      openai:
        api-key: ${OPENAI_API_KEY:}
        model: "gpt-3.5-turbo"
        base-url: "https://api.openai.com"
      deepseek:
        api-key: ${DEEPSEEK_API_KEY:}
        model: "deepseek-chat"
        base-url: "https://api.deepseek.com"
      lmstudio:
        base-url: ${LM_STUDIO_URL:http://localhost:1234}
        model: "default"
    
  cache:
    article-ttl: 300s           # 5 minutes
    featured-ttl: 3600s         # 1 hour
    crypto-ttl: 60s             # 1 minute
    ohlc-ttl: 300s              # 5 minutes
    
  performance:
    max-concurrent-rss: 5
    max-concurrent-llm: 3
    circuit-breaker-threshold: 5
    
  crypto:
    api:
      alternative-me-url: "https://api.alternative.me/v2/ticker/"
      coingecko-url: "https://api.coingecko.com/api/v3"
      kraken-url: "https://api.kraken.com/0/public"

# Resilience4j Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      rss-feeds:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 10s
      llm-service:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 3
        permitted-number-of-calls-in-half-open-state: 2
        wait-duration-in-open-state: 60s
        failure-rate-threshold: 60
        slow-call-rate-threshold: 60
        slow-call-duration-threshold: 30s
      crypto-api:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50

  retry:
    instances:
      rss-feeds:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
      llm-service:
        max-attempts: 2
        wait-duration: 2s
        exponential-backoff-multiplier: 2

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,cache
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
      slo:
        http.server.requests: 10ms,50ms,100ms,200ms,500ms

# Logging Configuration
logging:
  level:
    com.cryptoapp: INFO
    reactor.netty.http.client: DEBUG
    org.springframework.data.r2dbc: DEBUG
    org.springframework.web.reactive: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  r2dbc:
    pool:
      initial-size: 5
      max-size: 20
  
  data:
    redis:
      lettuce:
        pool:
          max-active: 8
          max-idle: 4

app:
  rss:
    sync-interval: 300000        # 5 minutes for development
  performance:
    max-concurrent-rss: 3
    max-concurrent-llm: 2

logging:
  level:
    com.cryptoapp: DEBUG
    reactor.netty: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    com.cryptoapp: INFO
    reactor.netty.http.client: WARN
    org.springframework.data.r2dbc: WARN
    org.springframework.web.reactive: WARN